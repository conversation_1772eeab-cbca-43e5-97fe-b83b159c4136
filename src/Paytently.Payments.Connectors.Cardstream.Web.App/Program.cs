using Paytently.Core.Api.Extensions;
using Paytently.Core.ApiClient.Extensions;
using Paytently.Core.AWS.SQS;
using Paytently.Core.Configuration;
using Paytently.Core.OpenTelemetry.Api;
using Paytently.Payments.Connectors.Cardstream.Web.App;
using Paytently.Payments.Connectors.Cardstream.Web.App.Extensions;
using Paytently.Payments.Connectors.Cardstream.Web.App.Features.AcsFlow.Services;
using Paytently.Payments.Connectors.Cardstream.Web.App.Features.Redirect.Services;
using Paytently.Payments.Connectors.Cardstream.Web.App.Services;
using Serilog;
using Serilog.Events;

ILogger logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateBootstrapLogger();

try
{
    WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
    builder.Configuration.AddPaytentlyConfiguration();
    builder.Services.AddApiServices(AvailableVersions.Versions, Constants.ApiVersions.v2);
    builder.UsePaytentlyTelemetry();

    builder.Services.AddSingleton<IWebSocketProvider, WebSocketProvider>();
    builder.Services.AddSingleton<AuthorizationUrlRedirectProvider>();
    builder.Services.AddSingleton<AcsFlowRedirectProvider>();
    builder.Services.AddSingleton<IWaiter, Waiter>();
    builder.Services.AddDefaultResilientApi<IPaymentsApi, PaymentsApi>();
    builder.Services.RegisterSQSPublisher();
    builder.Services.AddHttpContextAccessor();
    builder.Services.AddScoped<HttpContext>(sp =>
        sp.GetService<IHttpContextAccessor>()?.HttpContext ?? throw new InvalidOperationException()
    );

    WebApplication app = builder.Build();

    WebSocketOptions options = new() { KeepAliveInterval = TimeSpan.FromSeconds(30), };
    app.UseWebSockets(options);
    app.ConfigureApi().RegisterEndpoints().Run();
}
catch (Exception e)
{
    logger.Error(e, "error");
}
